<?php

class JsonDatabase
{
    private $dbDir;

    public function __construct($dbDir)
    {
        $this->dbDir = $dbDir;
        $this->initializeDatabase();
    }

    /**
     * Initialize database directory and create necessary folders
     */
    private function initializeDatabase()
    {
        if (!file_exists($this->dbDir)) {
            mkdir($this->dbDir, 0755, true);
        }

        // Create tables directories
        $tables = ['users', 'groups', 'vouchers', 'transactions', 'support_tickets'];
        foreach ($tables as $table) {
            $tableDir = $this->dbDir . '/' . $table;
            if (!file_exists($tableDir)) {
                mkdir($tableDir, 0755, true);
            }
        }
    }

    /**
     * Get file path for a record
     */
    private function getFilePath($table, $id)
    {
        return $this->dbDir . '/' . $table . '/' . $id . '.json';
    }

    /**
     * Save data to JSON file
     */
    public function save($table, $id, $data)
    {
        $filePath = $this->getFilePath($table, $id);
        $data['id'] = $id;
        $data['updated_at'] = time();
        
        return file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) !== false;
    }

    /**
     * Load data from JSON file
     */
    public function load($table, $id)
    {
        $filePath = $this->getFilePath($table, $id);
        
        if (!file_exists($filePath)) {
            return null;
        }

        $content = file_get_contents($filePath);
        return json_decode($content, true);
    }

    /**
     * Check if record exists
     */
    public function exists($table, $id)
    {
        return file_exists($this->getFilePath($table, $id));
    }

    /**
     * Delete a record
     */
    public function delete($table, $id)
    {
        $filePath = $this->getFilePath($table, $id);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return false;
    }

    /**
     * Get all records from a table
     */
    public function getAll($table)
    {
        $tableDir = $this->dbDir . '/' . $table;
        $records = [];

        if (!is_dir($tableDir)) {
            return $records;
        }

        $files = glob($tableDir . '/*.json');
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $data = json_decode($content, true);
            if ($data) {
                $records[] = $data;
            }
        }

        return $records;
    }

    /**
     * Update specific fields of a record
     */
    public function update($table, $id, $updateData)
    {
        $existingData = $this->load($table, $id);
        
        if ($existingData === null) {
            return false;
        }

        $mergedData = array_merge($existingData, $updateData);
        return $this->save($table, $id, $mergedData);
    }

    /**
     * Create or update user record
     */
    public function saveUser($userId, $userData = [])
    {
        $defaultUserData = [
            'id' => $userId,
            'step' => 'start',
            'data' => [],
            'join_time' => time(),
            'invite_from' => null,
            'created_at' => time(),
            'token' => $this->generateUserToken($userId)
        ];

        $userData = array_merge($defaultUserData, $userData);
        return $this->save('users', $userId, $userData);
    }

    /**
     * Get user data
     */
    public function getUser($userId)
    {
        return $this->load('users', $userId);
    }

    /**
     * Update user step
     */
    public function updateUserStep($userId, $step)
    {
        return $this->update('users', $userId, ['step' => $step]);
    }

    /**
     * Update user data
     */
    public function updateUserData($userId, $data)
    {
        return $this->update('users', $userId, ['data' => $data]);
    }

    /**
     * Add balance to user wallet
     */
    public function addUserBalance($userId, $amount)
    {
        $user = $this->getUser($userId);
        if (!$user) {
            return false;
        }

        $userData = $user['data'] ?? [];
        $currentBalance = isset($userData['balance']) ? $userData['balance'] : 0;
        $userData['balance'] = $currentBalance + $amount;

        return $this->updateUserData($userId, $userData);
    }

    /**
     * Get user balance
     */
    public function getUserBalance($userId)
    {
        $user = $this->getUser($userId);
        if (!$user) {
            return 0;
        }

        return isset($user['data']['balance']) ? $user['data']['balance'] : 0;
    }

    /**
     * Subtract balance from user wallet
     */
    public function subtractUserBalance($userId, $amount)
    {
        $user = $this->getUser($userId);
        if (!$user) {
            return false;
        }

        $userData = $user['data'] ?? [];
        $currentBalance = isset($userData['balance']) ? $userData['balance'] : 0;

        // Check if user has enough balance
        if ($currentBalance < $amount) {
            return false;
        }

        $userData['balance'] = $currentBalance - $amount;
        return $this->updateUserData($userId, $userData);
    }

    /**
     * Get all users
     */
    public function getAllUsers()
    {
        return $this->getAll('users');
    }

    /**
     * Save voucher data
     */
    public function saveVoucher($voucherId, $voucherData)
    {
        $defaultVoucherData = [
            'id' => $voucherId,
            'code' => '',
            'amount' => 0,
            'currency' => 'USD',
            'status' => 'active',
            'created_by' => null,
            'used_by' => null,
            'created_at' => time(),
            'used_at' => null
        ];

        $voucherData = array_merge($defaultVoucherData, $voucherData);
        return $this->save('vouchers', $voucherId, $voucherData);
    }

    /**
     * Get voucher data
     */
    public function getVoucher($voucherId)
    {
        return $this->load('vouchers', $voucherId);
    }

    /**
     * Find voucher by code
     */
    public function findVoucherByCode($code)
    {
        $vouchers = $this->getAll('vouchers');
        
        foreach ($vouchers as $voucher) {
            if ($voucher['code'] === $code) {
                return $voucher;
            }
        }
        
        return null;
    }

    /**
     * Get all vouchers
     */
    public function getAllVouchers()
    {
        return $this->getAll('vouchers');
    }

    /**
     * Save transaction data
     */
    public function saveTransaction($transactionId, $transactionData)
    {
        $defaultTransactionData = [
            'id' => $transactionId,
            'user_id' => null,
            'voucher_id' => null,
            'amount' => 0,
            'currency' => 'USD',
            'type' => 'voucher_use',
            'status' => 'completed',
            'created_at' => time()
        ];

        $transactionData = array_merge($defaultTransactionData, $transactionData);
        return $this->save('transactions', $transactionId, $transactionData);
    }

    /**
     * Get transaction data
     */
    public function getTransaction($transactionId)
    {
        return $this->load('transactions', $transactionId);
    }

    /**
     * Get all transactions
     */
    public function getAllTransactions()
    {
        return $this->getAll('transactions');
    }

    /**
     * Get user transactions
     */
    public function getUserTransactions($userId)
    {
        $transactions = $this->getAll('transactions');
        $userTransactions = [];

        foreach ($transactions as $transaction) {
            if ($transaction['user_id'] == $userId) {
                $userTransactions[] = $transaction;
            }
        }

        return $userTransactions;
    }

    /**
     * Generate unique token for user
     */
    public function generateUserToken($userId)
    {
        // Generate a unique token in format: $BIT-XXXXXXXXXXXXXXXXXXXXXXX
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $token = '$BIT-';

        // Generate 23 random characters
        for ($i = 0; $i < 23; $i++) {
            $token .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Make sure token is unique
        while ($this->isTokenExists($token)) {
            $token = '$BIT-';
            for ($i = 0; $i < 23; $i++) {
                $token .= $characters[rand(0, strlen($characters) - 1)];
            }
        }

        return $token;
    }

    /**
     * Check if token already exists
     */
    private function isTokenExists($token)
    {
        $users = $this->getAll('users');
        foreach ($users as $user) {
            if (isset($user['token']) && $user['token'] === $token) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get user by token
     */
    public function getUserByToken($token)
    {
        $users = $this->getAll('users');
        foreach ($users as $user) {
            if (isset($user['token']) && $user['token'] === $token) {
                return $user;
            }
        }
        return null;
    }

    /**
     * Get database statistics
     */
    public function getStats()
    {
        return [
            'users_count' => count($this->getAll('users')),
            'vouchers_count' => count($this->getAll('vouchers')),
            'transactions_count' => count($this->getAll('transactions')),
            'groups_count' => count($this->getAll('groups')),
            'orders_count' => count($this->getAll('orders')),
            'support_tickets_count' => count($this->getAll('support_tickets'))
        ];
    }



    /**
     * Get data from a specific collection by ID
     */
    public function get($collection, $id)
    {
        $filePath = $this->dbDir . '/' . $collection . '/' . $id . '.json';
        if (!file_exists($filePath)) {
            return null;
        }

        $content = file_get_contents($filePath);
        return json_decode($content, true);
    }




}
