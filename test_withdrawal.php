<?php

// Test withdrawal functionality
require_once 'config.php';
require_once 'JsonDatabase.php';

// Initialize JSON Database
$db = new JsonDatabase($JSON_DB_DIR);

// Test user ID
$test_user_id = 123456789;

echo "=== تست عملکرد برداشت موجودی ===\n\n";

// Create test user if not exists
if (!$db->exists('users', $test_user_id)) {
    $userData = [
        'first_name' => 'Test User',
        'step' => 'verified',
        'data' => ['balance' => 100000] // 100,000 تومان موجودی اولیه
    ];
    $db->saveUser($test_user_id, $userData);
    echo "✅ کاربر تست ایجاد شد با موجودی 100,000 تومان\n";
}

// Get initial balance
$initial_balance = $db->getUserBalance($test_user_id);
echo "💰 موجودی اولیه: " . number_format($initial_balance) . " تومان\n";

// Test withdrawal amount
$withdrawal_amount = 50000;
echo "💸 مبلغ برداشت: " . number_format($withdrawal_amount) . " تومان\n";

// Test subtractUserBalance function
echo "\n--- تست تابع subtractUserBalance ---\n";
$result = $db->subtractUserBalance($test_user_id, $withdrawal_amount);

if ($result) {
    echo "✅ تابع subtractUserBalance موفق بود\n";
    
    // Check new balance
    $new_balance = $db->getUserBalance($test_user_id);
    echo "💰 موجودی جدید: " . number_format($new_balance) . " تومان\n";
    
    $expected_balance = $initial_balance - $withdrawal_amount;
    if ($new_balance == $expected_balance) {
        echo "✅ موجودی به درستی کسر شد\n";
    } else {
        echo "❌ خطا: موجودی به درستی کسر نشد\n";
        echo "   انتظار: " . number_format($expected_balance) . " تومان\n";
        echo "   واقعی: " . number_format($new_balance) . " تومان\n";
    }
    
    // Test transaction saving
    echo "\n--- تست ذخیره تراکنش ---\n";
    $transactionId = 'TEST-WTH-' . time() . '-' . $test_user_id;
    $transaction_result = $db->saveTransaction($transactionId, [
        'user_id' => $test_user_id,
        'amount' => $withdrawal_amount,
        'currency' => 'IRR',
        'type' => 'withdrawal',
        'status' => 'completed',
        'card_info' => '1234-5678-9012-3456 : Test User',
        'created_at' => time()
    ]);
    
    if ($transaction_result) {
        echo "✅ تراکنش با موفقیت ذخیره شد\n";
        echo "🔍 کد تراکنش: $transactionId\n";
        
        // Verify transaction
        $saved_transaction = $db->getTransaction($transactionId);
        if ($saved_transaction && $saved_transaction['status'] == 'completed') {
            echo "✅ تراکنش با وضعیت 'completed' ذخیره شد\n";
        } else {
            echo "❌ خطا در ذخیره تراکنش\n";
        }
    } else {
        echo "❌ خطا در ذخیره تراکنش\n";
    }
    
} else {
    echo "❌ تابع subtractUserBalance ناموفق بود\n";
    echo "   ممکن است موجودی کافی نباشد یا خطای دیگری رخ داده باشد\n";
}

echo "\n=== پایان تست ===\n";

?>
